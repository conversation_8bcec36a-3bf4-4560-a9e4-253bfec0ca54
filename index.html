<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON> Photography Website</title>
    <script>
      // Handle OAuth callback redirect for SPA - preserve query parameters
      if (window.location.pathname === '/auth/callback' || window.location.search.includes('code=')) {
        console.log('OAuth callback detected in HTML, preserving parameters');
        const params = window.location.search;
        if (params.includes('code=')) {
          // Keep the parameters but change the path to root
          window.history.replaceState({}, document.title, '/' + params);
        }
      }
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
