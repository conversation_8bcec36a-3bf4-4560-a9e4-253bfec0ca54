import React from 'react';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: 'url(https://images.pexels.com/photos/133394/pexels-photo-133394.jpeg?auto=compress&cs=tinysrgb&w=1920&h=1080&fit=crop)'
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <div className="mb-6">
          <p className="text-xl md:text-2xl mb-4 font-light tracking-wide">
            "You do life. We'll capture the memory."
          </p>
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
            Capturing Awe-Inspiring Moments in Tennessee
          </h1>
        </div>

        <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto leading-relaxed font-light">
          Life flows like a stream, but some moments deserve to be stopped, captured, and cherished forever. 
          Let us help you preserve the memories that matter most.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={scrollToContact}
            className="bg-orange-500 text-white px-8 py-4 rounded-full hover:bg-orange-600 transition-all duration-300 font-medium text-lg flex items-center space-x-2 transform hover:scale-105"
          >
            <span>Capture Your Memory</span>
            <ArrowRight size={20} />
          </button>
          
          <button
            onClick={() => document.getElementById('portfolio')?.scrollIntoView({ behavior: 'smooth' })}
            className="border-2 border-white text-white px-8 py-4 rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300 font-medium text-lg"
          >
            View Portfolio
          </button>
        </div>

        {/* Featured Work Preview */}
        <div className="mt-16 grid grid-cols-2 gap-4 max-w-2xl mx-auto">
          <div className="text-center">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 mb-2">
              <img 
                src="https://images.pexels.com/photos/133394/pexels-photo-133394.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
                alt="Wildlife Photography"
                className="w-full h-32 object-cover rounded"
              />
            </div>
            <p className="text-sm font-medium">Wildlife</p>
          </div>
          <div className="text-center">
            <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4 mb-2">
              <img 
                src="https://images.pexels.com/photos/1618269/pexels-photo-1618269.jpeg?auto=compress&cs=tinysrgb&w=400&h=300&fit=crop"
                alt="Sports Photography"
                className="w-full h-32 object-cover rounded"
              />
            </div>
            <p className="text-sm font-medium">Sports</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;