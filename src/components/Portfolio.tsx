import React, { useState } from 'react';
import { X, ChevronLeft, ChevronRight, Settings } from 'lucide-react';
import GooglePhotosGallery from './GooglePhotosGallery';
import GooglePhotosAuth from './GooglePhotosAuth';
import { GooglePhoto } from '../services/googlePhotos';
import { googlePhotosService } from '../services/googlePhotos';

interface Photo {
  id: number;
  src: string;
  alt: string;
  category: string;
}

const Portfolio: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState<Photo | GooglePhoto | null>(null);
  const [showGooglePhotos, setShowGooglePhotos] = useState(false);
  const [showAuthPanel, setShowAuthPanel] = useState(false);
  
  const photos: Photo[] = [
    // Wildlife Photos
    { id: 1, src: 'https://images.pexels.com/photos/133394/pexels-photo-133394.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Majestic Eagle', category: 'wildlife' },
    { id: 2, src: 'https://images.pexels.com/photos/68669/pexels-photo-68669.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Forest Wildlife', category: 'wildlife' },
    { id: 3, src: 'https://images.pexels.com/photos/2662116/pexels-photo-2662116.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Bird in Flight', category: 'wildlife' },
    { id: 4, src: 'https://images.pexels.com/photos/1170986/pexels-photo-1170986.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Nature Wildlife', category: 'wildlife' },
    
    // Sports Photos
    { id: 5, src: 'https://images.pexels.com/photos/1618269/pexels-photo-1618269.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Baseball Action', category: 'sports' },
    { id: 6, src: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Football Game', category: 'sports' },
    { id: 7, src: 'https://images.pexels.com/photos/1153370/pexels-photo-1153370.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Youth Sports', category: 'sports' },
    { id: 8, src: 'https://images.pexels.com/photos/3621104/pexels-photo-3621104.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Athletic Moment', category: 'sports' },
    
    // Real Estate Photos
    { id: 9, src: 'https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Beautiful Home', category: 'realestate' },
    { id: 10, src: 'https://images.pexels.com/photos/1396132/pexels-photo-1396132.jpeg?auto=compress&cs=tinysrgb&w=800&h=600&fit=crop', alt: 'Interior Design', category: 'realestate' },
  ];

  const categories = [
    { id: 'all', name: 'All Work' },
    { id: 'wildlife', name: 'Wildlife' },
    { id: 'sports', name: 'Sports' },
    { id: 'realestate', name: 'Real Estate' }
  ];

  const filteredPhotos = selectedCategory === 'all' 
    ? photos 
    : photos.filter(photo => photo.category === selectedCategory);

  const openLightbox = (photo: Photo) => {
    setLightboxImage(photo);
    document.body.style.overflow = 'hidden';
  };

  const openGooglePhotoLightbox = (photo: GooglePhoto) => {
    const lightboxPhoto = {
      id: parseInt(photo.id.slice(-8), 16), // Convert to number for compatibility
      src: googlePhotosService.getPhotoUrl(photo.baseUrl, 1920, 1080),
      alt: photo.filename,
      category: selectedCategory === 'all' ? 'google' : selectedCategory
    };
    setLightboxImage(lightboxPhoto);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setLightboxImage(null);
    document.body.style.overflow = 'auto';
  };

  const navigateLightbox = (direction: 'prev' | 'next') => {
    if (!lightboxImage) return;
    
    // Only navigate within static photos for now
    if ('baseUrl' in lightboxImage) return;
    
    const currentIndex = filteredPhotos.findIndex(photo => photo.id === (lightboxImage as Photo).id);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? filteredPhotos.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === filteredPhotos.length - 1 ? 0 : currentIndex + 1;
    }
    
    setLightboxImage(filteredPhotos[newIndex]);
  };

  return (
    <section id="portfolio" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Portfolio
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            "They speak of the glorious splendor of your majesty—and I will meditate on your wonderful works." - Psalm 145:5
          </p>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-orange-500 text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-orange-100 hover:text-orange-600 shadow-md'
              }`}
            >
              {category.name}
            </button>
          ))}
          
          {/* Google Photos Toggle */}
          <button
            onClick={() => setShowGooglePhotos(!showGooglePhotos)}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center space-x-2 ${
              showGooglePhotos
                ? 'bg-blue-500 text-white shadow-lg transform scale-105'
                : 'bg-white text-gray-700 hover:bg-blue-100 hover:text-blue-600 shadow-md'
            }`}
          >
            <Settings className="h-4 w-4" />
            <span>Live Albums</span>
          </button>
        </div>

        {/* Google Photos Auth Panel */}
        {showGooglePhotos && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-900">Google Photos Integration</h3>
              <button
                onClick={() => setShowAuthPanel(!showAuthPanel)}
                className="text-gray-600 hover:text-gray-800 text-sm"
              >
                {showAuthPanel ? 'Hide Settings' : 'Show Settings'}
              </button>
            </div>
            
            {showAuthPanel && (
              <div className="mb-6">
                <GooglePhotosAuth onAuthSuccess={() => setShowAuthPanel(false)} />
              </div>
            )}
            
            {/* Live Google Photos Gallery */}
            <GooglePhotosGallery
              category={selectedCategory === 'all' ? 'wildlife' : selectedCategory as any}
              albumName={selectedCategory === 'wildlife' ? 'Wildlife' : selectedCategory === 'sports' ? 'Sports' : 'Real Estate'}
              maxPhotos={selectedCategory === 'all' ? 6 : 12}
              onPhotoClick={openGooglePhotoLightbox}
            />
          </div>
        )}

        {/* Photo Grid */}
        {!showGooglePhotos && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredPhotos.map((photo) => (
            <div
              key={photo.id}
              className="group cursor-pointer overflow-hidden rounded-lg shadow-lg bg-white transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={() => openLightbox(photo)}
            >
              <div className="aspect-w-4 aspect-h-3 overflow-hidden">
                <img
                  src={photo.src}
                  alt={photo.alt}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                  <p className="text-white font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    View Image
                  </p>
                </div>
              </div>
            </div>
          ))}
          </div>
        )}

        {/* Special Sections */}
        {selectedCategory === 'wildlife' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Wildlife Photography</h3>
            <p className="text-gray-600 leading-relaxed">
              Capturing God's magnificent creation in its natural habitat. Each wildlife photograph tells a story 
              of the incredible beauty and wonder that surrounds us in Tennessee's diverse ecosystems.
            </p>
          </div>
        )}

        {selectedCategory === 'sports' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Sports Photography</h3>
            <p className="text-gray-600 leading-relaxed mb-4">
              Focus on enjoying the game while we capture the action. Our sports photography lets parents 
              be fully present in the moment, knowing that every crucial play and emotional victory is being preserved.
            </p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <h4 className="font-semibold text-blue-900 mb-2">Game Day Package</h4>
              <p className="text-blue-800">5 High-Resolution Digital Photos for $250</p>
            </div>
          </div>
        )}

        {selectedCategory === 'realestate' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Real Estate Photography</h3>
            <p className="text-gray-600 leading-relaxed">
              Professional photography to make your listings shine. Contact for details on how we can help 
              showcase your property in the best possible light.
            </p>
          </div>
        )}
      </div>

      {/* Lightbox */}
      {lightboxImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <button
            onClick={closeLightbox}
            className="absolute top-4 right-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <X size={32} />
          </button>
          
          <button
            onClick={() => navigateLightbox('prev')}
            className="absolute left-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <ChevronLeft size={32} />
          </button>
          
          <button
            onClick={() => navigateLightbox('next')}
            className="absolute right-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <ChevronRight size={32} />
          </button>
          
          <img
            src={'src' in lightboxImage ? lightboxImage.src : googlePhotosService.getPhotoUrl((lightboxImage as any).baseUrl, 1920, 1080)}
            alt={lightboxImage.alt}
            className="max-w-full max-h-full object-contain"
          />
          
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center">
            <p className="text-lg font-medium">{lightboxImage.alt}</p>
          </div>
        </div>
      )}
    </section>
  );
};

export default Portfolio;