import React, { useState } from 'react';
import { X, Chevron<PERSON><PERSON><PERSON>, ChevronRight, Settings } from 'lucide-react';
import GooglePhotosGallery from './GooglePhotosGallery';
import GooglePhotosAuth from './GooglePhotosAuth';
import { GooglePhoto } from '../services/googlePhotos';
import { googlePhotosService } from '../services/googlePhotos';

interface Photo {
  id: number;
  src: string;
  alt: string;
  category: string;
}

const Portfolio: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [lightboxImage, setLightboxImage] = useState<Photo | GooglePhoto | null>(null);
  const [showGooglePhotos, setShowGooglePhotos] = useState(false);
  const [showAuthPanel, setShowAuthPanel] = useState(false);
  
  const photos: Photo[] = [
    // Wildlife Photos
    { id: 1, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a982c6cb1cde1f6e960.webp', alt: 'Wildlife Photography', category: 'wildlife' },
    { id: 2, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a984669a95105772219.webp', alt: 'Nature Wildlife', category: 'wildlife' },
    { id: 3, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98a392d00de30dd0d4.webp', alt: 'Bird Photography', category: 'wildlife' },
    { id: 4, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98a392d048090dd0d3.webp', alt: 'Forest Wildlife', category: 'wildlife' },
    { id: 5, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98caca582c1822f8d7.webp', alt: 'Wildlife Portrait', category: 'wildlife' },
    { id: 6, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98caca587f9322f8d6.webp', alt: 'Animal Photography', category: 'wildlife' },

    // Sports Photos
    { id: 7, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98e581e04bec34c2d7.webp', alt: 'Sports Action', category: 'sports' },
    { id: 8, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a98f670206fb8fc705f.webp', alt: 'Athletic Moment', category: 'sports' },
    { id: 9, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a997f1b992cfdb3d608.webp', alt: 'Game Action', category: 'sports' },
    { id: 10, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/yioc9gxnGLaNC1p4s1CX/media/685c1a997f1b994d9fb3d607.webp', alt: 'Youth Sports', category: 'sports' },
    { id: 11, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f48950dfe6756e215c26.webp', alt: 'Baseball Action', category: 'sports' },
    { id: 12, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489d0c8df0321e1126e.webp', alt: 'Football Game', category: 'sports' },
    { id: 13, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f48942e8ef583415f8e8.webp', alt: 'Sports Photography', category: 'sports' },
    { id: 14, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489ca34e31b662b9251.webp', alt: 'Team Sports', category: 'sports' },

    // Real Estate Photos
    { id: 15, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489ac9458bf7ab026d9.webp', alt: 'Beautiful Home', category: 'realestate' },
    { id: 16, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f4891a74cddc316bb5f5.webp', alt: 'Interior Design', category: 'realestate' },
    { id: 17, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489d0c8df639ce1126d.webp', alt: 'Property Photography', category: 'realestate' },
    { id: 18, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f4898b2950b7fe704e46.webp', alt: 'Real Estate Listing', category: 'realestate' },
    { id: 19, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f4899f0e4cc6b9193982.webp', alt: 'Home Photography', category: 'realestate' },
    { id: 20, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489ca34e3a6b62b9250.webp', alt: 'Architectural Photography', category: 'realestate' },
    { id: 21, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f4899224d790e683200f.webp', alt: 'Luxury Home', category: 'realestate' },
    { id: 22, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f489ac9458b521b026d8.webp', alt: 'Modern Interior', category: 'realestate' },
    { id: 23, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec99ca34e38fe92b8bb3.webp', alt: 'Property Showcase', category: 'realestate' },
    { id: 24, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec991a74cd9d3a6baec6.webp', alt: 'Estate Photography', category: 'realestate' },
    { id: 25, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec9950dfe6a15b215664.webp', alt: 'Home Exterior', category: 'realestate' },
    { id: 26, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec99ca34e30d0c2b8bb2.webp', alt: 'Real Estate Detail', category: 'realestate' },
    { id: 27, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec999f0e4c7fff193470.webp', alt: 'Professional Property', category: 'realestate' },
    { id: 28, src: 'https://images.leadconnectorhq.com/image/f_webp/q_80/r_1200/u_https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855ec9950dfe6699b215665.webp', alt: 'Residential Photography', category: 'realestate' },
    { id: 29, src: 'https://storage.googleapis.com/msgsndr/aQYV8jwYWM9za5egdIl2/media/686738689ca6fba4f4182282.webp', alt: 'Premium Property', category: 'realestate' }
  ];

  const categories = [
    { id: 'all', name: 'All Work' },
    { id: 'wildlife', name: 'Wildlife' },
    { id: 'sports', name: 'Sports' },
    { id: 'realestate', name: 'Real Estate' }
  ];

  const filteredPhotos = selectedCategory === 'all' 
    ? photos 
    : photos.filter(photo => photo.category === selectedCategory);

  const openLightbox = (photo: Photo) => {
    setLightboxImage(photo);
    document.body.style.overflow = 'hidden';
  };

  const openGooglePhotoLightbox = (photo: GooglePhoto) => {
    const lightboxPhoto = {
      id: parseInt(photo.id.slice(-8), 16), // Convert to number for compatibility
      src: googlePhotosService.getPhotoUrl(photo.baseUrl, 1920, 1080),
      alt: photo.filename,
      category: selectedCategory === 'all' ? 'google' : selectedCategory
    };
    setLightboxImage(lightboxPhoto);
    document.body.style.overflow = 'hidden';
  };

  const closeLightbox = () => {
    setLightboxImage(null);
    document.body.style.overflow = 'auto';
  };

  const navigateLightbox = (direction: 'prev' | 'next') => {
    if (!lightboxImage) return;
    
    // Only navigate within static photos for now
    if ('baseUrl' in lightboxImage) return;
    
    const currentIndex = filteredPhotos.findIndex(photo => photo.id === (lightboxImage as Photo).id);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex === 0 ? filteredPhotos.length - 1 : currentIndex - 1;
    } else {
      newIndex = currentIndex === filteredPhotos.length - 1 ? 0 : currentIndex + 1;
    }
    
    setLightboxImage(filteredPhotos[newIndex]);
  };

  return (
    <section id="portfolio" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Portfolio
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            "They speak of the glorious splendor of your majesty—and I will meditate on your wonderful works." - Psalm 145:5
          </p>
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-orange-500 text-white shadow-lg transform scale-105'
                  : 'bg-white text-gray-700 hover:bg-orange-100 hover:text-orange-600 shadow-md'
              }`}
            >
              {category.name}
            </button>
          ))}
          
          {/* Google Photos Toggle */}
          <button
            onClick={() => setShowGooglePhotos(!showGooglePhotos)}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-300 flex items-center space-x-2 ${
              showGooglePhotos
                ? 'bg-blue-500 text-white shadow-lg transform scale-105'
                : 'bg-white text-gray-700 hover:bg-blue-100 hover:text-blue-600 shadow-md'
            }`}
          >
            <Settings className="h-4 w-4" />
            <span>Live Albums</span>
          </button>
        </div>

        {/* Google Photos Auth Panel */}
        {showGooglePhotos && (
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-semibold text-gray-900">Google Photos Integration</h3>
              <button
                onClick={() => setShowAuthPanel(!showAuthPanel)}
                className="text-gray-600 hover:text-gray-800 text-sm"
              >
                {showAuthPanel ? 'Hide Settings' : 'Show Settings'}
              </button>
            </div>
            
            {showAuthPanel && (
              <div className="mb-6">
                <GooglePhotosAuth onAuthSuccess={() => setShowAuthPanel(false)} />
              </div>
            )}
            
            {/* Live Google Photos Gallery */}
            <GooglePhotosGallery
              category={selectedCategory === 'all' ? 'wildlife' : selectedCategory as any}
              albumName={selectedCategory === 'wildlife' ? 'Wildlife' : selectedCategory === 'sports' ? 'Sports' : 'Real Estate'}
              maxPhotos={selectedCategory === 'all' ? 6 : 12}
              onPhotoClick={openGooglePhotoLightbox}
            />
          </div>
        )}

        {/* Photo Grid */}
        {!showGooglePhotos && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredPhotos.map((photo) => (
            <div
              key={photo.id}
              className="group cursor-pointer overflow-hidden rounded-lg shadow-lg bg-white transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={() => openLightbox(photo)}
            >
              <div className="aspect-w-4 aspect-h-3 overflow-hidden">
                <img
                  src={photo.src}
                  alt={photo.alt}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                  <p className="text-white font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    View Image
                  </p>
                </div>
              </div>
            </div>
          ))}
          </div>
        )}

        {/* Special Sections */}
        {selectedCategory === 'wildlife' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Wildlife Photography</h3>
            <p className="text-gray-600 leading-relaxed">
              Capturing God's magnificent creation in its natural habitat. Each wildlife photograph tells a story 
              of the incredible beauty and wonder that surrounds us in Tennessee's diverse ecosystems.
            </p>
          </div>
        )}

        {selectedCategory === 'sports' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Sports Photography</h3>
            <p className="text-gray-600 leading-relaxed mb-4">
              Focus on enjoying the game while we capture the action. Our sports photography lets parents 
              be fully present in the moment, knowing that every crucial play and emotional victory is being preserved.
            </p>
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <h4 className="font-semibold text-blue-900 mb-2">Game Day Package</h4>
              <p className="text-blue-800">5 High-Resolution Digital Photos for $250</p>
            </div>
          </div>
        )}

        {selectedCategory === 'realestate' && (
          <div className="bg-white rounded-lg p-8 shadow-lg max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">Real Estate Photography</h3>
            <p className="text-gray-600 leading-relaxed">
              Professional photography to make your listings shine. Contact for details on how we can help 
              showcase your property in the best possible light.
            </p>
          </div>
        )}
      </div>

      {/* Lightbox */}
      {lightboxImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <button
            onClick={closeLightbox}
            className="absolute top-4 right-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <X size={32} />
          </button>
          
          <button
            onClick={() => navigateLightbox('prev')}
            className="absolute left-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <ChevronLeft size={32} />
          </button>
          
          <button
            onClick={() => navigateLightbox('next')}
            className="absolute right-4 text-white hover:text-orange-500 transition-colors duration-200"
          >
            <ChevronRight size={32} />
          </button>
          
          <img
            src={'src' in lightboxImage ? lightboxImage.src : googlePhotosService.getPhotoUrl((lightboxImage as any).baseUrl, 1920, 1080)}
            alt={lightboxImage.alt}
            className="max-w-full max-h-full object-contain"
          />
          
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-center">
            <p className="text-lg font-medium">{lightboxImage.alt}</p>
          </div>
        </div>
      )}
    </section>
  );
};

export default Portfolio;