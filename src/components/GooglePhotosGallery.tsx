import React, { useState, useEffect } from 'react';
import { Camera, RefreshCw, AlertCircle, ExternalLink } from 'lucide-react';
import { googlePhotosService, GooglePhoto, GoogleAlbum } from '../services/googlePhotos';

interface GooglePhotosGalleryProps {
  albumName?: string;
  category: 'wildlife' | 'sports' | 'realestate';
  maxPhotos?: number;
  onPhotoClick?: (photo: GooglePhoto) => void;
}

const GooglePhotosGallery: React.FC<GooglePhotosGalleryProps> = ({
  albumName,
  category,
  maxPhotos = 12,
  onPhotoClick
}) => {
  const [photos, setPhotos] = useState<GooglePhoto[]>([]);
  const [albums, setAlbums] = useState<GoogleAlbum[]>([]);
  const [selectedAlbum, setSelectedAlbum] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const hasAuth = googlePhotosService.loadStoredTokens();
    console.log('GooglePhotosGallery: Auth check:', { hasAuth });
    setIsAuthenticated(hasAuth);
    
    if (hasAuth) {
      loadAlbums();
    } else {
      console.log('GooglePhotosGallery: Not authenticated, checking for callback...');
      // Check if we just completed OAuth
      const checkCallback = async () => {
        const success = await googlePhotosService.handleOAuthCallback();
        if (success) {
          console.log('OAuth callback successful, reloading auth state');
          setIsAuthenticated(true);
          loadAlbums();
        }
      };
      checkCallback();
    }
  }, []);

  useEffect(() => {
    if (selectedAlbum) {
      loadPhotos(selectedAlbum);
    }
  }, [selectedAlbum, maxPhotos]);

  const loadAlbums = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const albumList = await googlePhotosService.getAlbums();
      setAlbums(albumList);
      
      // Auto-select album if albumName is provided
      if (albumName) {
        const matchingAlbum = albumList.find(album => 
          album.title.toLowerCase().includes(albumName.toLowerCase()) ||
          album.title.toLowerCase().includes(category)
        );
        if (matchingAlbum) {
          setSelectedAlbum(matchingAlbum.id);
        }
      }
    } catch (err) {
      setError('Failed to load albums');
      console.error('Error loading albums:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const loadPhotos = async (albumId: string) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const photoList = await googlePhotosService.getAlbumPhotos(albumId, maxPhotos);
      setPhotos(photoList.slice(0, maxPhotos));
    } catch (err) {
      setError('Failed to load photos');
      console.error('Error loading photos:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    if (selectedAlbum) {
      loadPhotos(selectedAlbum);
    } else {
      loadAlbums();
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="bg-gray-50 rounded-lg p-8 text-center">
        <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Google Photos Not Connected</h3>
        <p className="text-gray-600 mb-4">
          Connect your Google Photos account to display live albums here.
        </p>
        <button
          onClick={() => window.location.href = googlePhotosService.getAuthUrl()}
          className="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-200 flex items-center space-x-2 mx-auto"
        >
          <ExternalLink className="h-4 w-4" />
          <span>Connect Google Photos</span>
        </button>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <AlertCircle className="h-6 w-6 text-red-600" />
            <div>
              <span className="text-red-800 font-medium">Error Loading Photos</span>
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Retry</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Album Selector */}
      {albums.length > 0 && (
        <div className="flex items-center space-x-4">
          <label htmlFor="album-select" className="text-sm font-medium text-gray-700">
            Select Album:
          </label>
          <select
            id="album-select"
            value={selectedAlbum}
            onChange={(e) => setSelectedAlbum(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          >
            <option value="">Choose an album...</option>
            {albums.map((album) => (
              <option key={album.id} value={album.id}>
                {album.title} ({album.mediaItemsCount} photos)
              </option>
            ))}
          </select>
          <button
            onClick={handleRefresh}
            className="p-2 text-gray-600 hover:text-orange-600 transition-colors duration-200"
            title="Refresh"
          >
            <RefreshCw className={`h-5 w-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
          <span className="ml-3 text-gray-600">Loading photos...</span>
        </div>
      )}

      {/* Photos Grid */}
      {!isLoading && photos.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {photos.map((photo) => (
            <div
              key={photo.id}
              className="group cursor-pointer overflow-hidden rounded-lg shadow-lg bg-white transform transition-all duration-300 hover:scale-105 hover:shadow-xl"
              onClick={() => onPhotoClick?.(photo)}
            >
              <div className="aspect-w-4 aspect-h-3 overflow-hidden">
                <img
                  src={googlePhotosService.getPhotoUrl(photo.baseUrl, 800, 600)}
                  alt={photo.filename}
                  className="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-500"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                  <p className="text-white font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    View Image
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* No Photos State */}
      {!isLoading && photos.length === 0 && selectedAlbum && (
        <div className="text-center py-12">
          <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Photos Found</h3>
          <p className="text-gray-600">
            The selected album appears to be empty or photos couldn't be loaded.
          </p>
        </div>
      )}

      {/* No Album Selected */}
      {!isLoading && !selectedAlbum && albums.length > 0 && (
        <div className="text-center py-12">
          <Camera className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Album</h3>
          <p className="text-gray-600">
            Choose an album from the dropdown above to display photos.
          </p>
        </div>
      )}
    </div>
  );
};

export default GooglePhotosGallery;